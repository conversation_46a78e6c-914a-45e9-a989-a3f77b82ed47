import {
  DeleteOutlined,
  EditOutlined,
  PlayCircleOutlined,
  PlusOutlined,
  StopOutlined,
} from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { history, useModel } from '@umijs/max';
import { Button, Modal, Space, Tag } from 'antd';
import dayjs from 'dayjs';
import React, { useRef, useEffect } from 'react';
import './index.less';

const { confirm } = Modal;

/**
 * 问卷列表页面
 */
const QuestionnaireList: React.FC = () => {
  const {
    questionnaireList,
    total,
    fetchQuestionnaireList,
    updateQuestionnaireStatusAction,
    deleteQuestionnaireAction,
  } = useModel('questionnaire');

  const actionRef = useRef<ActionType>();

  // 监听页面可见性变化，从其他页面返回时刷新数据
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && actionRef.current) {
        actionRef.current.reload();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // 搜索处理
  const handleSearch = async (params: any) => {
    const queryParams: API.IQuestionnaireQuery = {
      page: params.current || 1,
      limit: params.pageSize || 10,
      month: params.month,
      status: params.status,
    };

    await fetchQuestionnaireList(queryParams);
    return {
      data: questionnaireList,
      total,
      success: true,
    };
  };

  // 状态标签渲染
  const renderStatusTag = (status: string) => {
    const statusMap = {
      draft: { color: 'default', text: '草稿' },
      published: { color: 'success', text: '发布' },
      closed: { color: 'error', text: '关闭' },
    };
    const config = statusMap[status as keyof typeof statusMap] || {
      color: 'default',
      text: status,
    };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 状态变更
  const handleStatusChange = (id: number, status: string) => {
    const statusText = status === 'published' ? '发布' : '关闭';
    confirm({
      title: `确认${statusText}问卷？`,
      content: `确定要${statusText}这个问卷吗？`,
      onOk: async () => {
        const result = await updateQuestionnaireStatusAction(id, { status });
        if (result && actionRef.current) {
          actionRef.current.reload();
        }
      },
    });
  };

  // 删除问卷
  const handleDelete = (id: number) => {
    confirm({
      title: '确认删除？',
      content: '删除后无法恢复，确定要删除这个问卷吗？',
      okType: 'danger',
      onOk: async () => {
        const result = await deleteQuestionnaireAction(id);
        if (result && actionRef.current) {
          actionRef.current.reload();
        }
      },
    });
  };

  // 操作按钮
  const renderActions = (record: any) => {
    return (
      <Space>
        <Button
          type="link"
          size="small"
          icon={<EditOutlined />}
          onClick={() => history.push(`/questionnaire/edit/${record.id}`)}
        >
          编辑
        </Button>

        {record.status === 'draft' && (
          <Button
            type="link"
            size="small"
            icon={<PlayCircleOutlined />}
            onClick={() => handleStatusChange(record.id, 'published')}
          >
            发布
          </Button>
        )}

        {record.status === 'published' && (
          <Button
            type="link"
            size="small"
            icon={<StopOutlined />}
            onClick={() => handleStatusChange(record.id, 'closed')}
          >
            关闭
          </Button>
        )}

        <Button
          type="link"
          size="small"
          danger
          icon={<DeleteOutlined />}
          onClick={() => handleDelete(record.id)}
        >
          删除
        </Button>
      </Space>
    );
  };

  // 表格列定义
  const columns: ProColumns<any>[] = [
    {
      title: '问卷标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
      search: false,
    },
    {
      title: '月份',
      dataIndex: 'month',
      key: 'month',
      width: 120,
      valueType: 'dateMonth',
      render: (_, record) => dayjs(record.month).format('YYYY-MM'),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      valueType: 'select',
      valueEnum: {
        draft: { text: '草稿', status: 'Default' },
        published: { text: '发布', status: 'Success' },
        closed: { text: '关闭', status: 'Error' },
      },
      render: (_, record) => renderStatusTag(record.status),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      search: false,
      sorter: true,
      render: (_, record) =>
        dayjs(record.created_at).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      search: false,
      render: (_, record) => renderActions(record),
    },
  ];

  return (
    <div className="questionnaire-list">
      <ProTable<any>
        actionRef={actionRef}
        columns={columns}
        request={handleSearch}
        rowKey="id"
        search={{
          labelWidth: 'auto',
        }}
        toolBarRender={() => [
          <Button
            key="create"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => history.push('/questionnaire/create')}
          >
            创建问卷
          </Button>,
        ]}
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`,
        }}
        options={{
          reload: true,
          density: false,
          setting: false,
        }}
      />
    </div>
  );
};

export default QuestionnaireList;
